import cx from 'classnames';
import {getCurrentPage} from '@baidu/vita-utils-shared';
import {login, eventCenter} from '@tarojs/taro';
import {View, Button} from '@tarojs/components';
import React, {FC, memo, useCallback, useState, useMemo, useEffect, useRef} from 'react';

import {useGetUrlParams} from '../../../pages-im/src/hooks/common';

import {navigate} from '../../../pages-im/src/utils/basicAbility/commonNavigate';
import {WX_LOGIN_PATH} from '../../../pages-im/src/constants/path';
import {initPlugin} from '../../../pages-im/src/utils/generalFunction/loginApi';
import {ubcCommonViewSend, ubcCommonClkSend} from '../../../pages-im/src/utils/generalFunction/ubc';
import {
    toLoginWithApi,
    getWechatPassUserInfo
} from '../../../pages-im/src/utils/generalFunction/login';
import {reportWeAnalysisEvent} from '../reportWeAnalysisEvent/index';
import CLoginPopup from '../c-login-popup/index.weapp';

import styles from './index.module.less';
import {LoginButtonProps} from './index.d';

// 放全局是因为login组件之间公用数据
let __isLogin: boolean | string | number = false;

const LoginButton: FC<LoginButtonProps> = (props: LoginButtonProps) => {
    const {
        children,
        ubcValue,
        className,
        onCallBack,
        callbackUrl,
        onLoginFail,
        onLoginSuccess,
        isLogin = false,
        isLoginPopup = false,
        wxLoginInteractionType = 'page'
    } = props;

    const query = useGetUrlParams();

    __isLogin = isLogin;
    const isLoginTriager = useRef<boolean>(false);
    const [wxCode, setWxCode] = useState('');
    const [loginParams, setLoginParams] = useState<unknown>(null);

    /**
     *
     * @description 当前页面是否是小程序的登录页
     */
    const inWechatMiniLoginPage = useMemo(() => {
        let result = false;

        const curPage = getCurrentPage();
        if (curPage.route === WX_LOGIN_PATH) {
            result = true;
        }

        return result;
    }, []);

    /**
     * @description 需要打开登录弹窗
     */
    const needOpenLoginPopup = useMemo(() => {
        const {bduss} = getWechatPassUserInfo();

        return !bduss && !isLoginPopup && wxLoginInteractionType === 'popup';
    }, [isLoginPopup, wxLoginInteractionType]);

    const callBack = useCallback(() => {
        onCallBack?.();
    }, [onCallBack]);

    /**
     * 登录成功回调
     * @event onLoginSuccess
     */
    const loginSuccess = useCallback(
        (event = {}) => {
            onLoginSuccess?.({
                ...event,
                loginStatus: true
            });

            __isLogin = true;
            if (ubcValue) {
                ubcCommonClkSend({
                    value: ubcValue
                });
            }
        },
        [onLoginSuccess, ubcValue]
    );

    /**
     *
     * @description 处理登录
     */
    const dealLoginClick = useCallback(() => {
        try {
            // 打开登录弹窗
            if (needOpenLoginPopup) {
                eventCenter.trigger('showWxLoginPopup', {});
                isLoginTriager.current = true;

                // 通过 eventCenter 监听登录状态变化，触发相关回调
                // Tips: 由于设计多次已有监听 on 无法对齐重新覆盖，修改为 once
                eventCenter.once('wxLoginStatusChange', res => {
                    if (isLoginTriager.current && wxLoginInteractionType === 'popup') {
                        const {type} = res;

                        if (type === 'logout') {
                            onLoginFail?.();
                        } else if (type === 'login') {
                            loginSuccess({});
                        }
                    }
                });

                reportWeAnalysisEvent({
                    event: 'clk_quick_login_shadow',
                    properties: {
                        interaction_of_login: isLoginPopup ? 'popup' : 'page',
                        login_error_query: JSON.stringify(query),
                        login_error_tips: '登录页影子按钮被点击，触发登录弹窗',
                        is_error: 0
                    }
                });
                // 多个登录组件公用登录状态
            } else if (__isLogin) {
                loginSuccess?.();

                reportWeAnalysisEvent({
                    event: 'clk_quick_login_shadow',
                    properties: {
                        interaction_of_login: isLoginPopup ? 'popup' : 'page',
                        login_error_query: JSON.stringify(query),
                        login_error_tips:
                            '登录页影子按钮被点击，组件公用登录状态为 true，直接触发登录成功回调',
                        is_error: 0
                    }
                });
            } else {
                const curPage = getCurrentPage();

                if (loginParams) {
                    // 登录失败重试
                    toLoginWithApi(loginParams);

                    reportWeAnalysisEvent({
                        event: 'clk_quick_login_shadow',
                        properties: {
                            interaction_of_login: isLoginPopup ? 'popup' : 'page',
                            login_error_query: JSON.stringify(query),
                            login_error_tips: '登录页影子按钮被点击，触发登录失败重试',
                            is_error: 1
                        }
                    });
                } else {
                    // 跳转登录页
                    let to = `/${curPage?.path}`;
                    if (callbackUrl) {
                        to = callbackUrl;
                    }

                    const {bduss} = getWechatPassUserInfo();
                    // 增加兜底如果用户已经登录，直接跳转落地页
                    if (bduss) {
                        navigate({
                            url: to,
                            openType: 'redirect'
                        });

                        reportWeAnalysisEvent({
                            event: 'clk_quick_login_shadow',
                            properties: {
                                interaction_of_login: isLoginPopup ? 'popup' : 'page',
                                login_error_query: JSON.stringify(query),
                                login_error_tips:
                                    '登录页影子按钮被点击，登录状态为 true，兜底跳转落地页',
                                is_error: 1
                            }
                        });
                    } else {
                        const msg = {
                            PageStatus: 1,
                            to,
                            sourcePage: curPage?.path
                        };

                        reportWeAnalysisEvent({
                            event: 'clk_quick_login_shadow',
                            properties: {
                                interaction_of_login: isLoginPopup ? 'popup' : 'page',
                                login_error_query: JSON.stringify(query),
                                login_error_tips:
                                    '登录页影子按钮被点击, 登录状态为 false，触发微信登录页',
                                is_error: 1
                            }
                        });

                        navigate({
                            url: WX_LOGIN_PATH,
                            openType: 'navigate',
                            params: {
                                msg: encodeURIComponent(JSON.stringify(msg))
                            }
                        });
                    }
                }
            }
        } catch (err) {
            reportWeAnalysisEvent({
                event: 'login_error',
                properties: {
                    interaction_of_login: isLoginPopup ? 'popup' : 'page',
                    login_error_query: '',
                    login_error_tips: `登录页 CloginBtn dealLoginClick 执行出错：${JSON.stringify(err)}`
                }
            });
        }
    }, [
        query,
        callbackUrl,
        isLoginPopup,
        loginParams,
        loginSuccess,
        needOpenLoginPopup,
        onLoginFail,
        wxLoginInteractionType
    ]);

    /**
     * 调用手机号快捷登录能力
     */
    const loginByPhoneNumber = useCallback(
        async e => {
            const params = {
                params: {...e, wxCode},
                failCallback: onLoginFail,
                successCallback: () => {
                    // 登录信息提示，打印保留
                    console.info('successCallback 执行');
                }, // 登录成功后，回调使用 promise 处理保证
                // successCallback: loginSuccess,
                interactionType: wxLoginInteractionType,
                backUrl: callbackUrl || ''
            };
            setLoginParams(params);
            e.stopPropagation();

            await toLoginWithApi(params);
            loginSuccess({});

            reportWeAnalysisEvent({
                event: 'clk_quick_login',
                properties: {
                    interaction_of_login: wxLoginInteractionType
                }
            });
            if (ubcValue) {
                ubcCommonViewSend({
                    value: ubcValue
                });
            }
        },
        [wxLoginInteractionType, wxCode, onLoginFail, loginSuccess, ubcValue, callbackUrl]
    );

    /**
     *
     * @description 渲染登录按钮
     */
    const renderLoginBtn = useMemo(() => {
        ubcCommonViewSend({
            value: 'wx_showlogin_button'
        });

        return (
            <Button
                openType='getPhoneNumber'
                onGetPhoneNumber={loginByPhoneNumber}
                className={styles.mall_login_button_loginButton}
            />
        );
    }, [loginByPhoneNumber]);

    /**
     *
     * @description 页面初始化获取 login 的 code 用来 pass 登录（code 获取必须在 button 点击之前）
     */
    useEffect(() => {
        (async () => {
            if (isLoginPopup || inWechatMiniLoginPage) {
                const {code} = await login();
                code && setWxCode(code);
            }
        })();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    useEffect(() => {
        initPlugin({
            backUrl: callbackUrl || ''
        });
    }, [callbackUrl]);

    /**
     * @description 需要渲染原生按钮触发登录
     */
    const needRenderNativeButton = useMemo(() => {
        try {
            const {bduss} = getWechatPassUserInfo();

            return (
                !bduss && // 未登录
                ((isLoginPopup && wxLoginInteractionType === 'popup') || // 登录弹窗触发并且是弹窗登录
                    (inWechatMiniLoginPage && wxLoginInteractionType === 'page')) // 登录页触发并且是页面登录
            );
        } catch (err) {
            console.error('needRenderNativeButton 执行出错：', err);

            return true;
        }
    }, [inWechatMiniLoginPage, isLoginPopup, wxLoginInteractionType]);

    const memoLoginPopup = useMemo(() => {
        if (!needOpenLoginPopup) {
            return null;
        }
        return <CLoginPopup />;
    }, [needOpenLoginPopup]);

    /**
     * 如果未登录仅渲染children, 这样保证children上的event事件可以执行 ）；
     * 如果已经登录 渲染children和登录元素（renderLoginedStatus）；
     * 根据event冒泡原理，children上挂载的onClick会阻塞外层登录元素的执行，
     * 为了保证登录事件执行，使用css手段，登录元素（renderLoginedStatus）绝对定位覆盖在children
     */
    // TODO: 弹层登录，BDUSS 失效时，的处理逻辑待优化；@wanghaoyu08
    return (
        <View className={cx(styles.mall_login_button, className)} onClick={callBack}>
            {needRenderNativeButton ? (
                renderLoginBtn
            ) : (
                <View className={styles.mall_login_button_loginButton} onClick={dealLoginClick} />
            )}
            {children}
            {memoLoginPopup}
        </View>
    );
};

LoginButton.defaultProps = {
    isLogin: false,
    callbackUrl: '',
    useH5QuickLogin: false
};

export default memo(LoginButton);
