import cx from 'classnames';
import {View, Text} from '@tarojs/components';
import {WImage} from '@baidu/wz-taro-tools-core';
import {CLoginButton} from '@baidu/vita-ui-cards-common';
import {memo, type FC, useEffect, useCallback, useRef} from 'react';
import {debounce} from 'lodash-es';
import {useGetUrlParams} from '../../../../hooks/common';
import {useGetSessionId, useGetUserData} from '../../../../hooks/triageStream/pageDataController';
// import {useGetCardEventCallback} from '../../../../hooks/useGetCardEventCallback';
import {useHandleUserLoginBizAction} from '../../../../hooks/triageStream/useHandleUserBizAction';
import MyOrderList from '../../../ImInput/capsuleTools/orderInfoV2';
import FeatureBubble from '../FeatureBubble';
import {ubcCommonClkSend, ubcCommonViewSend} from '../../../../utils/generalFunction/ubc';
import {useBubbleData, useBubbleVisibility} from '../../hooks/useBubbleData';
import {useClickOutside} from '../../../../hooks/useClickOutside';
import type {OrderGuideTipType} from './index.d';
import styles from './index.module.less';

const UBC = {
    BASE_VALUE: 'top_tips_order_guide',
    VIEW: 'view',
    CLICK: 'clk'
} as const;

const DEBOUNCETIME = 1000;

/**
 * 订单引导提示组件
 *
 * 用于显示订单引导提示，包含标题信息和操作按钮
 * 处理用户导航和交互追踪
 *
 * @param {OrderGuideTipType} props - 组件属性
 * @param {Object} props.titleInfo - 标题区域信息
 * @param {Object} props.newConversationInfo - 新建会话按钮信息
 * @param {Object} props.historyRecordInfo - 历史记录按钮信息
 */
const OrderGuideTip: FC<OrderGuideTipType> = ({titleInfo, btnTools}) => {
    // const {onOpenLink} = useGetCardEventCallback();
    const {isDirected = 0} = useGetUrlParams();
    const {userData} = useGetUserData() || {};
    const {isLogin} = userData || {};
    const curSessionId = useGetSessionId();
    const {handleLoginBizAction} = useHandleUserLoginBizAction();
    const orderListRef = useRef<{closeOrderBubble: () => void} | null>(null);
    const bubbleData = useBubbleData(titleInfo);

    const {
        isVisible: isFeatureBubbleVisible,
        showBubble,
        hideBubble
    } = useBubbleVisibility(bubbleData);
    // 组件挂载或 isDirected 变化时触发埋点
    useEffect(() => {
        ubcCommonViewSend({
            value: `${UBC.BASE_VALUE}_${UBC.VIEW}`,
            ext: {
                product_info: {
                    // eslint-disable-next-line camelcase
                    isDirected
                }
            }
        });
    }, [isDirected]);

    /**
     * 处理导航链接点击事件
     * @param {Object} key - 动作信息对象
     * @param {string} ubcVal - UBC 埋点值
     */
    // const handleClickJump = useCallback(
    //     (key, ubcVal: string) => {
    //         const {interactionInfo} = key?.actionInfo || {};

    //         if (interactionInfo) {
    //             onOpenLink({info: interactionInfo});
    //         }

    //         ubcCommonClkSend({
    //             value: `${UBC.BASE_VALUE}_${ubcVal}_${UBC.CLICK}`,
    //             ext: {
    //                 // eslint-disable-next-line camelcase
    //                 product_info: {isDirected}
    //             }
    //         });
    //     },
    //     [isDirected, onOpenLink]
    // );
    /**
     * 处理操作按钮点击事件
     * @param {Object} actionInfo - 动作信息对象
     */
    const handleClickBtn = useCallback(
        (type: string) => {
            if (type === 'myOrderList') handleLoginBizAction();
            ubcCommonClkSend({
                value: `${UBC.BASE_VALUE}_${type}_${UBC.CLICK}`,
                ext: {
                    // eslint-disable-next-line camelcase
                    product_info: {sessionId: curSessionId}
                }
            });
        },
        [curSessionId, handleLoginBizAction]
    );

    const renderBtnDom = useCallback(
        info => {
            console.info('isLogin11', isLogin)
            if (!info) return null;
            const {avatar, title, type} = info;
            return (
                <CLoginButton
                    isLogin={isLogin}
                    closeShowNewUserTag={true}
                    useH5CodeLogin={true}
                    onLoginFail={error => {
                        console.error('error', error);
                    }}
                    onLoginSuccess={debounce(() => handleClickBtn(type), DEBOUNCETIME, {
                        leading: true,
                        trailing: false
                    })}
                >
                    <View className={cx(styles.actionButton, 'wz-flex wz-col-center')}>
                        <WImage className={cx(styles.buttonIcon, 'wz-mr-9')} src={avatar} />
                        <View>{title}</View>
                    </View>
                </CLoginButton>
            );
        },
        [handleClickBtn, isLogin]
    );

    // 点击titleInfo时显示彩蛋气泡
    const handleTitleInfoClick = useCallback(() => {
        // 1. 关闭订单气泡
        orderListRef.current?.closeOrderBubble();
        if (isFeatureBubbleVisible) {
            hideBubble();
            return;
        }
        // 2. 显示气泡
        showBubble();

        // 埋点
        ubcCommonClkSend({
            value: 'ImHeaderReportGuide',
            ext: {
                // eslint-disable-next-line camelcase
                product_info: {}
            }
        });
    }, [hideBubble, isFeatureBubbleVisible, showBubble]);

    // 点击外部时隐藏气泡
    useClickOutside(['leftIcon'], () => {
        hideBubble();
    });

    return (
        <View className={cx(styles.orderGuideTip, 'wz-flex wz-row-left wz-fs-42 wz-plr-45')}>
            {titleInfo && (
                <View
                    className={cx(styles.titleInfo, 'wz-flex wz-row-center')}
                    // onClick={() => handleClickJump(titleInfo, 'title')}
                >
                    <WImage
                        className={cx(styles.leftIcon, 'wz-mr-24')}
                        src={titleInfo?.avatar}
                        mode='aspectFill'
                        shape='circle'
                        onClick={handleTitleInfoClick}
                        id='leftIcon'
                    />
                    <Text>{titleInfo?.title || ''}</Text>
                    {isFeatureBubbleVisible && (
                        <FeatureBubble
                            isVisible={isFeatureBubbleVisible}
                            onHide={hideBubble}
                            contentList={bubbleData}
                        />
                    )}
                </View>
            )}
            <View className={cx(styles.actionButtons, 'wz-flex wz-row-center')}>
                {btnTools?.map(item => renderBtnDom(item))}
            </View>
            {isLogin && (
                <View className={cx(styles.orderInfoList, 'wz-flex')}>
                    <MyOrderList ref={orderListRef} />
                </View>
            )}
        </View>
    );
};
export default memo(OrderGuideTip);
